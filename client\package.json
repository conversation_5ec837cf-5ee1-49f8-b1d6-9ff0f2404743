{"name": "client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint --fix --dir src && prettier --write ."}, "dependencies": {"@headlessui/react": "^2.2.7", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.84.1", "@tanstack/react-query-devtools": "^5.84.1", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "framer-motion": "^12.23.12", "lucide-react": "^0.536.0", "nanoid": "^5.1.5", "next": "15.4.5", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.1", "zod": "^4.0.14", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tanstack/eslint-plugin-query": "^5.83.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.5", "eslint-config-prettier": "^10.1.8", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}