{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ESNext",
    "moduleResolution": "node",
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "skipLibCheck": true,
    "types": ["node"],
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "baseUrl": "./", // for aliases
    "paths": {
      "@utils/*": ["src/utils/*"],
      "@configs/*": ["src/configs/*"],
      "@controllers/*": ["src/controllers/*"],
      "@job/*": ["src/job/*"],
      "@lib/*": ["src/lib*"],
      "@middlewares/*": ["src/middlewares/*"],
      "@routes/*": ["src/routes/*"],
      "@services/*": ["src/services/*"],
      "@types/*": ["src/types/*"],
      "@validator/*": ["src/validator/*"],
      "@repositories/*": ["src/repositories/*"],
       "@/*": ["src/*"]
    },
    "outDir": "dist", // compiled JS files
    "allowJs": true,
    "noEmit": false,
    "resolveJsonModule": true
  },
  "include": ["src", "index.ts", "scripts","logs","pnpm-lock.yaml","src/api"],
  "exclude": ["node_modules", "dist"],
  "tsc-alias": {
    "resolveFullPaths": true, // Ensures full paths are resolved, including extensions
    "verbose": false // Set to true if you want to see detailed output from tsc-alias
  }
}
