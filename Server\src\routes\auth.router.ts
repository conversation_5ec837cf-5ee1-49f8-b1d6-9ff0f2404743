import express, { Router } from "express";
import authController from "../controllers/auth.controller";
import { validateRequest } from "../middleware/validateRequest";
import { UserSigninSchema, UserSignupSchema } from "../validator/user.schema";
import { protect } from "@/middleware/authMiddleware";

const authRouter: Router = express.Router();

// Local Auth Routes
authRouter.post("/signup", validateRequest(UserSignupSchema), authController.signup);
authRouter.post("/signin", validateRequest(UserSigninSchema), authController.signin);
authRouter.post("/logout",protect, authController.logout);

export default authRouter;