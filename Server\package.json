{"name": "server", "version": "1.0.0", "description": "Backend for a SaaS cohort management platform where mentors can create and manage cohorts, and students can enroll, track progress, and interact within cohorts.", "type": "module", "main": "index.js", "scripts": {"dev": "nodemon", "copy-static": "cpx \"src/api/**/*.{yaml,yml,json}\" dist/src/api", "build": "tsc --project tsconfig.json && tsc-alias -p tsconfig.json && npm run copy-static", "start": "node dist/index.js", "clean": "rm -rf dist", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write src", "fix": "eslint src --ext .ts --fix && prettier --write src"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.13.1", "dependencies": {"@asteasolutions/zod-to-openapi": "^8.1.0", "@prisma/client": "^6.13.0", "aws-sdk": "^2.1692.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "chalk": "^5.4.1", "cloudinary": "^2.7.0", "compression": "^1.8.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cpx": "^1.5.0", "cron": "^4.3.3", "csurf": "^1.11.0", "dayjs": "^1.11.13", "dotenv": "^17.2.1", "express": "^5.1.0", "express-async-handler": "^1.2.0", "express-rate-limit": "^8.0.1", "express-zod-api": "^25.0.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "multer": "^2.0.2", "node-cache": "^5.1.2", "nodemailer": "^7.0.5", "nodemon": "^3.1.10", "razorpay": "^2.9.6", "resend": "^4.7.0", "sentry": "^0.1.2", "slugify": "^1.6.6", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tsx": "^4.20.3", "twilio": "^5.8.0", "uuid": "^11.1.0", "web-push": "^3.6.7", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "yamljs": "^0.3.0", "zod": "^4.0.14", "zod-to-openapi": "^0.2.1"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/compression": "^1.8.1", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/morgan": "^1.9.10", "@types/node": "^24.1.0", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/yamljs": "^0.2.34", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "globals": "^16.3.0", "jiti": "^2.5.1", "prettier": "^3.6.2", "prettier-eslint": "^16.4.2", "prisma": "^6.13.0", "ts-node-dev": "^2.0.0", "tsc-alias": "^1.8.16", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0"}}