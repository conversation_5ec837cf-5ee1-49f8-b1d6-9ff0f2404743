[2025-08-09 07:44:48] ERROR: [POST] /api/v1/auth/signin - Request body is missing
[2025-08-09 07:45:13] ERROR: [POST] /api/v1/auth/signin - Validation failed
[2025-08-09 07:46:20] ERROR: [POST] /api/v1/auth/signin - Validation failed
[2025-08-09 07:46:22] ERROR: [POST] /api/v1/auth/signin - Validation failed
[2025-08-09 07:46:37] ERROR: [POST] /api/v1/auth/signin - Validation failed
[2025-08-09 07:59:33] ERROR: Error Response: Validation failed
[2025-08-09 07:59:53] ERROR: Error Response: Validation failed
[2025-08-09 08:01:02] ERROR: Error Response: Validation failed
[2025-08-09 08:01:03] ERROR: Error Response: Validation failed
[2025-08-09 08:01:56] ERROR: Error Response: Validation failed
[2025-08-09 08:01:57] ERROR: Error Response: Validation failed
[2025-08-09 08:02:18] ERROR: Error Response: Request body is missing
[2025-08-09 08:03:17] ERROR: Error Response: Validation failed
[2025-08-09 08:06:28] ERROR: Error Response: Validation failed
[2025-08-09 08:06:52] ERROR: Error Response: Validation failed
[2025-08-09 08:13:40] ERROR: Error Response: Request body is missing
[2025-08-09 08:13:52] ERROR: Error Response: Request body is missing
[2025-08-09 08:13:58] ERROR: [POST] /api/v1/auth/signin - Expected ':' after property name in JSON at position 16 (line 3 column 1)
[2025-08-09 08:14:16] ERROR: Error Response: Validation failed
[2025-08-09 08:14:26] ERROR: Error Response: Validation failed
[2025-08-09 08:14:50] ERROR: Error Response: Validation failed
[2025-08-09 08:15:09] ERROR: Error Response: Validation failed
[2025-08-09 08:15:27] ERROR: [POST] /api/v1/auth/signin - INVALID_CREDENTIALS
[2025-08-09 08:15:27] ERROR: [POST] /api/v1/auth/signin - INVALID_CREDENTIALS
[2025-08-09 08:15:45] ERROR: Error Response: Request body is missing
[2025-08-09 08:15:51] ERROR: [POST] /api/v1/auth/signin - INVALID_CREDENTIALS
[2025-08-09 08:15:51] ERROR: [POST] /api/v1/auth/signin - INVALID_CREDENTIALS
[2025-08-09 08:16:15] ERROR: [POST] /api/v1/auth/signup - 
Invalid `prisma.user.create()` invocation in
C:\Users\<USER>\Desktop\Project Complate\betterShop.in\Server\src\repository\auth.repository.ts:10:30

   7   email: string;
   8   password: string;
   9 }): Promise<User> => {
→ 10   return await prisma.user.create({
         data: {
           email: "<EMAIL>",
           password: "$2b$10$QKfDeRKuiFqktqUoUuKBiOSlCBMOu7gk/Ep.HmSDpXsm5sqBnpoTe",
       +   provider: String
         }
       })

Argument `provider` is missing.
[2025-08-09 08:16:15] ERROR: [POST] /api/v1/auth/signup - 
Invalid `prisma.user.create()` invocation in
C:\Users\<USER>\Desktop\Project Complate\betterShop.in\Server\src\repository\auth.repository.ts:10:30

   7   email: string;
   8   password: string;
   9 }): Promise<User> => {
→ 10   return await prisma.user.create({
         data: {
           email: "<EMAIL>",
           password: "$2b$10$QKfDeRKuiFqktqUoUuKBiOSlCBMOu7gk/Ep.HmSDpXsm5sqBnpoTe",
       +   provider: String
         }
       })

Argument `provider` is missing.
[2025-08-09 08:20:21] ERROR: [POST] /api/v1/auth/signup - 
Invalid `prisma.user.create()` invocation in
C:\Users\<USER>\Desktop\Project Complate\betterShop.in\Server\src\repository\auth.repository.ts:10:30

   7   email: string;
   8   password: string;
   9 }): Promise<User> => {
→ 10   return await prisma.user.create({
         data: {
           email: "<EMAIL>",
           password: "$2b$10$BNVbEbjbXnWMo8vKXibFKeBtkAKktAPL6I7JUy5RZphcxNTneoicm",
           ~~~~~~~~
       ?   name?: String | Null,
       ?   password_hash?: String | Null,
       ?   mobile?: String | Null,
       ?   created_at?: DateTime,
       ?   updated_at?: DateTime,
       ?   role?: Role,
       ?   status?: UserStatus,
       ?   last_login_at?: DateTime | Null,
       ?   last_logout_at?: DateTime | Null,
       ?   failed_login_attempts?: Int,
       ?   last_failed_login_at?: DateTime | Null,
       ?   address?: NullableJsonNullValueInput | Json,
       ?   provider?: String | Null,
       ?   providerId?: String | Null,
       ?   passwordResetTokens?: PasswordResetTokenCreateNestedManyWithoutUserInput,
       ?   reviews?: ReviewCreateNestedManyWithoutUserInput,
       ?   cart?: CartCreateNestedOneWithoutUserInput,
       ?   orders?: OrderCreateNestedManyWithoutUserInput,
       ?   recommendations?: RecommendationCreateNestedOneWithoutUserInput,
       ?   notifications?: NotificationQueueCreateNestedManyWithoutUserInput,
       ?   wishlists?: WishlistCreateNestedManyWithoutUserInput,
       ?   UserBehaviorLog?: UserBehaviorLogCreateNestedManyWithoutUserInput
         }
       })

Unknown argument `password`. Available options are marked with ?.
[2025-08-09 08:20:21] ERROR: [POST] /api/v1/auth/signup - 
Invalid `prisma.user.create()` invocation in
C:\Users\<USER>\Desktop\Project Complate\betterShop.in\Server\src\repository\auth.repository.ts:10:30

   7   email: string;
   8   password: string;
   9 }): Promise<User> => {
→ 10   return await prisma.user.create({
         data: {
           email: "<EMAIL>",
           password: "$2b$10$BNVbEbjbXnWMo8vKXibFKeBtkAKktAPL6I7JUy5RZphcxNTneoicm",
           ~~~~~~~~
       ?   name?: String | Null,
       ?   password_hash?: String | Null,
       ?   mobile?: String | Null,
       ?   created_at?: DateTime,
       ?   updated_at?: DateTime,
       ?   role?: Role,
       ?   status?: UserStatus,
       ?   last_login_at?: DateTime | Null,
       ?   last_logout_at?: DateTime | Null,
       ?   failed_login_attempts?: Int,
       ?   last_failed_login_at?: DateTime | Null,
       ?   address?: NullableJsonNullValueInput | Json,
       ?   provider?: String | Null,
       ?   providerId?: String | Null,
       ?   passwordResetTokens?: PasswordResetTokenCreateNestedManyWithoutUserInput,
       ?   reviews?: ReviewCreateNestedManyWithoutUserInput,
       ?   cart?: CartCreateNestedOneWithoutUserInput,
       ?   orders?: OrderCreateNestedManyWithoutUserInput,
       ?   recommendations?: RecommendationCreateNestedOneWithoutUserInput,
       ?   notifications?: NotificationQueueCreateNestedManyWithoutUserInput,
       ?   wishlists?: WishlistCreateNestedManyWithoutUserInput,
       ?   UserBehaviorLog?: UserBehaviorLogCreateNestedManyWithoutUserInput
         }
       })

Unknown argument `password`. Available options are marked with ?.
[2025-08-09 08:29:48] ERROR: [POST] /api/v1/auth/signup - User already exists
[2025-08-09 08:29:48] ERROR: [POST] /api/v1/auth/signup - User already exists
[2025-08-09 09:38:48] ERROR: [POST] /api/v1/auth/signin - INVALID_CREDENTIALS
[2025-08-09 09:38:48] ERROR: [POST] /api/v1/auth/signin - INVALID_CREDENTIALS
[2025-08-09 09:39:05] ERROR: [POST] /api/v1/auth/signin - Expected double-quoted property name in JSON at position 41 (line 4 column 1)
[2025-08-09 09:39:13] ERROR: Error Response: Validation failed
