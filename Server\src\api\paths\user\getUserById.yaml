/users/{id}:
  get:
    summary: Get a user by ID
    tags:
      - Users
    parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
    responses:
      '200':
        description: A single user
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      '404':
        $ref: '#/components/responses/NotFound'