[2025-08-08 14:42:34] ERROR: [AppError] BODY_MISSING: Request body is missing
[2025-08-08 14:42:34] ERROR: [AppError] INTERNAL_VALIDATION_ERROR: Unexpected error
[2025-08-08 14:42:34] ERROR: [Cause] {"statusCode":400,"errorCode":"BODY_MISSING","isOperational":true,"name":"AppError"}
[2025-08-08 14:42:42] ERROR: [AppError] BODY_MISSING: Request body is missing
[2025-08-08 14:42:42] ERROR: [AppError] INTERNAL_VALIDATION_ERROR: Unexpected error
[2025-08-08 14:42:42] ERROR: [Cause] {"statusCode":400,"errorCode":"BODY_MISSING","isOperational":true,"name":"AppError"}
[2025-08-08 14:46:28] ERROR: [POST] /api/v1/auth/signin - Cannot destructure property 'email' of 'req.body' as it is undefined.
[2025-08-08 14:47:45] ERROR: [POST] /api/v1/auth/signin - Cannot destructure property 'email' of 'req.body' as it is undefined.
[2025-08-08 14:50:53] ERROR: [AppError] BODY_MISSING: Request body is missing
[2025-08-08 14:50:53] ERROR: [AppError] INTERNAL_VALIDATION_ERROR: Unexpected error
[2025-08-08 14:50:53] ERROR: [Cause] {"statusCode":400,"errorCode":"BODY_MISSING","isOperational":true,"name":"AppError"}
[2025-08-08 14:50:53] ERROR: [INTERNAL_VALIDATION_ERROR] Unexpected error
[2025-08-08 14:51:38] ERROR: [AppError] BODY_MISSING: Request body is missing
[2025-08-08 14:51:38] ERROR: [AppError] INTERNAL_VALIDATION_ERROR: Unexpected error
[2025-08-08 14:51:38] ERROR: [Cause] {"statusCode":400,"errorCode":"BODY_MISSING","isOperational":true,"name":"AppError"}
[2025-08-08 14:51:38] ERROR: [INTERNAL_VALIDATION_ERROR] Unexpected error
[2025-08-08 14:53:48] ERROR: [AppError] BODY_MISSING: Request body is missing
[2025-08-08 14:53:48] ERROR: [AppError] INTERNAL_VALIDATION_ERROR: Unexpected error
[2025-08-08 14:53:48] ERROR: [Cause] {"statusCode":400,"errorCode":"BODY_MISSING","isOperational":true,"name":"AppError"}
[2025-08-08 14:53:48] ERROR: [INTERNAL_VALIDATION_ERROR] Unexpected error
[2025-08-08 14:55:17] ERROR: [AppError] BODY_MISSING: Request body is missing
[2025-08-08 14:55:17] ERROR: [AppError] INTERNAL_VALIDATION_ERROR: Unexpected error
[2025-08-08 14:55:17] ERROR: [Cause] {"statusCode":400,"errorCode":"BODY_MISSING","isOperational":true,"name":"AppError"}
[2025-08-08 14:55:17] ERROR: [INTERNAL_VALIDATION_ERROR] Unexpected error
[2025-08-08 14:57:07] ERROR: [POST] /api/v1/auth/signin - Cannot destructure property 'email' of 'req.body' as it is undefined.
[2025-08-08 14:57:07] ERROR: [INTERNAL_SERVER_ERROR] Something went wrong
[2025-08-08 14:57:07] ERROR: TypeError: Cannot destructure property 'email' of 'req.body' as it is undefined.
    at <anonymous> (C:\Users\<USER>\Desktop\Project Complate\betterShop.in\Server\src\controllers\auth.controller.ts:18:13)
    at <anonymous> (C:\Users\<USER>\Desktop\Project Complate\betterShop.in\Server\src\utils\wrapAsync.ts:14:13)
    at asyncUtilWrap (C:\Users\<USER>\Desktop\Project Complate\betterShop.in\Server\node_modules\.pnpm\express-async-handler@1.2.0\node_modules\express-async-handler\index.js:3:20)
    at Layer.handleRequest (C:\Users\<USER>\Desktop\Project Complate\betterShop.in\Server\node_modules\.pnpm\router@2.2.0\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\Project Complate\betterShop.in\Server\node_modules\.pnpm\router@2.2.0\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\Project Complate\betterShop.in\Server\node_modules\.pnpm\router@2.2.0\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\Project Complate\betterShop.in\Server\node_modules\.pnpm\router@2.2.0\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Desktop\Project Complate\betterShop.in\Server\node_modules\.pnpm\router@2.2.0\node_modules\router\lib\layer.js:152:17)
    at C:\Users\<USER>\Desktop\Project Complate\betterShop.in\Server\node_modules\.pnpm\router@2.2.0\node_modules\router\index.js:295:15
    at processParams (C:\Users\<USER>\Desktop\Project Complate\betterShop.in\Server\node_modules\.pnpm\router@2.2.0\node_modules\router\index.js:582:12)
[2025-08-08 14:58:05] ERROR: [AppError] BODY_MISSING: Request body is missing
[2025-08-08 14:58:05] ERROR: [AppError] INTERNAL_VALIDATION_ERROR: Unexpected error
[2025-08-08 14:58:05] ERROR: [Cause] {"statusCode":400,"errorCode":"BODY_MISSING","isOperational":true,"name":"AppError"}
[2025-08-08 14:58:05] ERROR: [INTERNAL_VALIDATION_ERROR] Unexpected error
[2025-08-08 14:58:38] ERROR: [AppError] BODY_MISSING: Request body is missing
[2025-08-08 14:58:38] ERROR: [AppError] INTERNAL_VALIDATION_ERROR: Unexpected error
[2025-08-08 14:58:38] ERROR: [Cause] {"statusCode":400,"errorCode":"BODY_MISSING","isOperational":true,"name":"AppError"}
[2025-08-08 14:58:38] ERROR: [INTERNAL_VALIDATION_ERROR] Unexpected error
[2025-08-08 14:59:20] ERROR: [AppError] BODY_MISSING: Request body is missing
[2025-08-08 14:59:20] ERROR: [AppError] INTERNAL_VALIDATION_ERROR: Unexpected error
[2025-08-08 14:59:20] ERROR: [Cause] {"statusCode":400,"errorCode":"BODY_MISSING","isOperational":true,"name":"AppError"}
[2025-08-08 14:59:20] ERROR: [INTERNAL_VALIDATION_ERROR] Unexpected error
[2025-08-08 15:00:02] ERROR: [AppError] BODY_MISSING: Request body is missing
[2025-08-08 15:00:02] ERROR: [AppError] INTERNAL_VALIDATION_ERROR: Unexpected error
[2025-08-08 15:00:02] ERROR: [Cause] {"statusCode":400,"errorCode":"BODY_MISSING","isOperational":true,"name":"AppError"}
[2025-08-08 15:00:02] ERROR: [INTERNAL_VALIDATION_ERROR] Unexpected error
[2025-08-08 15:00:41] ERROR: [AppError] BODY_MISSING: Request body is missing
[2025-08-08 15:00:41] ERROR: [AppError] INTERNAL_VALIDATION_ERRORaaa: Unexpected error
[2025-08-08 15:00:41] ERROR: [Cause] {"statusCode":400,"errorCode":"BODY_MISSING","isOperational":true,"name":"AppError"}
[2025-08-08 15:00:41] ERROR: [INTERNAL_VALIDATION_ERRORaaa] Unexpected error
[2025-08-08 15:00:42] ERROR: [AppError] BODY_MISSING: Request body is missing
[2025-08-08 15:00:42] ERROR: [AppError] INTERNAL_VALIDATION_ERRORaaa: Unexpected error
[2025-08-08 15:00:42] ERROR: [Cause] {"statusCode":400,"errorCode":"BODY_MISSING","isOperational":true,"name":"AppError"}
[2025-08-08 15:00:42] ERROR: [INTERNAL_VALIDATION_ERRORaaa] Unexpected error
[2025-08-08 15:00:50] ERROR: [AppError] BODY_MISSING: Request body is missing
[2025-08-08 15:00:50] ERROR: [AppError] INTERNAL_VALIDATION_ERRORaaa: Unexpected error
[2025-08-08 15:00:50] ERROR: [Cause] {"statusCode":400,"errorCode":"BODY_MISSING","isOperational":true,"name":"AppError"}
[2025-08-08 15:00:50] ERROR: [INTERNAL_VALIDATION_ERRORaaa] Unexpected error
[2025-08-08 15:02:14] ERROR: [AppError] BODY_MISSING: Request body is missing
[2025-08-08 15:02:14] ERROR: [BODY_MISSING] Request body is missing
[2025-08-08 17:07:01] ERROR: [AppError] VALIDATION_ERROR: Validation failed
[2025-08-08 17:07:01] ERROR: [Details] {"formErrors":[],"fieldErrors":{"email":["Please enter a valid email address"],"password":["Password must be at least 8 characters"]}}
[2025-08-08 17:07:01] ERROR: [VALIDATION_ERROR] Validation failed
[2025-08-08 17:08:09] ERROR: [AppError] VALIDATION_ERROR: Validation failed
[2025-08-08 17:08:09] ERROR: [VALIDATION_ERROR] Validation failed
[2025-08-08 17:08:14] ERROR: [AppError] VALIDATION_ERROR: Validation failed
[2025-08-08 17:08:14] ERROR: [VALIDATION_ERROR] Validation failed
[2025-08-08 17:08:45] ERROR: [AppError] VALIDATION_ERROR: Validation failed
[2025-08-08 17:08:45] ERROR: [Details] {"formErrors":[],"fieldErrors":{"email":["Please enter a valid email address"],"password":["Password must be at least 8 characters"]}}
[2025-08-08 17:08:45] ERROR: [VALIDATION_ERROR] Validation failed
